'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Layout, Row, Col, Card, Form, Input, Button, Checkbox, Typography } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import type { FormProps } from 'antd'

const { Header } = Layout
const { Title } = Typography

type FieldType = {
  email?: string
  password?: string
  remember?: boolean
}

export default function Login() {
  const router = useRouter()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const onFinish: FormProps<FieldType>['onFinish'] = async (values) => {
    setLoading(true)
    try {
      console.log('Success:', values)
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      // Redirect to content page
      router.push('/content/corpsearch')
    } catch (error) {
      console.error('Login failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onReset = () => {
    form.resetFields()
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <Header style={{
        background: 'rgba(0, 21, 41, 0.8)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <div style={{
          color: '#fff',
          fontSize: '20px',
          fontWeight: 'bold',
          letterSpacing: '1px'
        }}>
          Spider Front
        </div>
      </Header>

      <div style={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }}>
        <Row justify="center" style={{ width: '100%' }}>
          <Col xs={22} sm={16} md={12} lg={8} xl={6}>
            <Card
              style={{
                borderRadius: '12px',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
                border: 'none',
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                padding: '40px 32px'
              }}
            >
              <div style={{ textAlign: 'center', marginBottom: '32px' }}>
                <Title level={2} style={{
                  margin: 0,
                  color: '#1f2937',
                  fontWeight: 600
                }}>
                  欢迎登录
                </Title>
                <p style={{
                  color: '#6b7280',
                  marginTop: '8px',
                  fontSize: '14px'
                }}>
                  请输入您的账户信息
                </p>
              </div>

              <Form
                form={form}
                name="login"
                layout="vertical"
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
                size="large"
              >
                <Form.Item<FieldType>
                  label="邮箱地址"
                  name="email"
                  rules={[
                    { required: true, message: '请输入邮箱地址!' },
                    { type: 'email', message: '请输入有效的邮箱地址!' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="请输入邮箱地址"
                    style={{ borderRadius: '8px' }}
                  />
                </Form.Item>

                <Form.Item<FieldType>
                  label="密码"
                  name="password"
                  rules={[{ required: true, message: '请输入密码!' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="请输入密码"
                    style={{ borderRadius: '8px' }}
                  />
                </Form.Item>

                <Form.Item<FieldType>
                  name="remember"
                  valuePropName="checked"
                  style={{ marginBottom: '24px' }}
                >
                  <Checkbox style={{ color: '#6b7280' }}>记住我</Checkbox>
                </Form.Item>

                <Form.Item style={{ marginBottom: '16px' }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    style={{
                      height: '48px',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: 500,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none'
                    }}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Form.Item>

                <Form.Item style={{ marginBottom: 0 }}>
                  <Button
                    onClick={onReset}
                    block
                    style={{
                      height: '40px',
                      borderRadius: '8px',
                      color: '#6b7280',
                      borderColor: '#d1d5db'
                    }}
                  >
                    重置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}
