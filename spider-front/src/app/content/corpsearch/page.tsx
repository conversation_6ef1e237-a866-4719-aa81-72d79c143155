'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  DatePicker,
  InputNumber,
  Select,
  Button,
  Tag,
  Radio,
  Table,
  Switch,
  Pagination,
  Modal,
  message,
  Space,
  Divider,
  Typography,
  Tooltip
} from 'antd'
import {
  DownloadOutlined,
  CloseOutlined,
  SearchOutlined,
  ReloadOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { exportTableToExcel } from '@/utils/excel'

const { RangePicker } = DatePicker
const { Option } = Select
const { Title, Text } = Typography

interface SearchForm {
  corpName: string
  handDate: [string, string] | null
  kilometerSmall: number | null
  kilometerBig: number | null
  constructStatus: string | null
  endDate: [string, string] | null
  priceSmall: number | null
  priceBig: number | null
  keyWords: string
  keyType: 'and' | 'or'
}

interface DataItem {
  key: string
  corpName: string
  projectName: string
  technologyGrade: string
  province: string
  beginDate: string
  handDate: string
  endDate: string
  kilometer: string
  contractPrice: string
  settlementPrice: string
  remark: string
}

export default function CorpSearch() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<DataItem[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [showIndex, setShowIndex] = useState(false)
  const [keyWordsStr, setKeyWordsStr] = useState('')
  const [keywordsArray, setKeywordsArray] = useState<string[]>([])
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([])

  const constructStatusOptions = [
    { label: '总包已建', value: '总包已建' },
    { label: '总包在建', value: '总包在建' }
  ]

  // Watch keyWordsStr changes
  useEffect(() => {
    if (keyWordsStr === '') {
      setKeywordsArray([])
      return
    }
    setKeywordsArray(keyWordsStr.split(' ').filter(word => word.trim() !== ''))
  }, [keyWordsStr])

  const columns: ColumnsType<DataItem> = [
    ...(showIndex ? [{
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      fixed: 'left' as const,
      align: 'center' as const,
      render: (_: any, __: DataItem, index: number) => (
        <span style={{
          display: 'inline-block',
          width: '24px',
          height: '24px',
          lineHeight: '24px',
          background: '#f0f2f5',
          borderRadius: '50%',
          fontSize: '12px',
          color: '#666'
        }}>
          {(pageIndex - 1) * pageSize + index + 1}
        </span>
      )
    }] : []),
    {
      title: '企业名称',
      dataIndex: 'corpName',
      key: 'corpName',
      width: 200,
      fixed: 'left' as const,
      sorter: true,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip title={text}>
          <Text strong style={{ color: '#1f2937' }}>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '工程名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 180,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip title={text}>
          <Text>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '技术等级',
      dataIndex: 'technologyGrade',
      key: 'technologyGrade',
      width: 120,
      filters: [
        { text: '高速公路', value: '高速公路' },
        { text: '一级公路', value: '一级公路' },
        { text: '二级公路', value: '二级公路' },
        { text: '三级公路', value: '三级公路' },
        { text: '特大型桥梁工程', value: '特大型桥梁工程' },
        { text: '其他工程', value: '其他工程' },
      ],
      onFilter: (value, record) => record.technologyGrade === value,
      render: (text: string) => {
        const colorMap: Record<string, string> = {
          '高速公路': 'red',
          '一级公路': 'orange',
          '二级公路': 'gold',
          '三级公路': 'green',
          '特大型桥梁工程': 'blue',
          '其他工程': 'default'
        }
        return <Tag color={colorMap[text] || 'default'}>{text}</Tag>
      },
    },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
      width: 100,
      render: (text: string) => (
        <Text style={{ color: '#4b5563' }}>{text}</Text>
      ),
    },
    {
      title: '开工日期',
      dataIndex: 'beginDate',
      key: 'beginDate',
      width: 110,
      sorter: true,
      render: (text: string) => (
        <Text style={{ fontSize: '12px', color: '#6b7280' }}>{text}</Text>
      ),
    },
    {
      title: '交工日期',
      dataIndex: 'handDate',
      key: 'handDate',
      width: 110,
      sorter: true,
      render: (text: string) => (
        <Text style={{ fontSize: '12px', color: '#6b7280' }}>{text}</Text>
      ),
    },
    {
      title: '竣工日期',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 110,
      sorter: true,
      render: (text: string) => (
        <Text style={{ fontSize: '12px', color: '#6b7280' }}>{text}</Text>
      ),
    },
    {
      title: '公里数(km)',
      dataIndex: 'kilometer',
      key: 'kilometer',
      width: 100,
      align: 'right' as const,
      render: (text: string) => (
        <Text style={{ color: '#059669', fontWeight: 500 }}>{text}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      align: 'center' as const,
      render: (_, record, index) => (
        <Button
          type="link"
          size="small"
          onClick={() => showInfo(record, index)}
          style={{ padding: '4px 8px' }}
        >
          查看详情
        </Button>
      ),
    },
  ]

  const showInfo = (record: DataItem, index: number) => {
    Modal.info({
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <InfoCircleOutlined style={{ color: '#1890ff' }} />
          <span>项目详细信息</span>
        </div>
      ),
      width: 600,
      content: (
        <div style={{ padding: '16px 0' }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card size="small" style={{ background: '#f8f9fa' }}>
                <Title level={5} style={{ margin: '0 0 12px 0', color: '#1f2937' }}>
                  基本信息
                </Title>
                <Row gutter={[16, 12]}>
                  <Col span={12}>
                    <Text strong>企业名称：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.corpName}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>工程名称：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.projectName}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>技术等级：</Text>
                    <br />
                    <Tag color="blue">{record.technologyGrade}</Tag>
                  </Col>
                  <Col span={12}>
                    <Text strong>省份：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.province}</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={24}>
              <Card size="small" style={{ background: '#f0f9ff' }}>
                <Title level={5} style={{ margin: '0 0 12px 0', color: '#1f2937' }}>
                  时间信息
                </Title>
                <Row gutter={[16, 12]}>
                  <Col span={8}>
                    <Text strong>开工日期：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.beginDate}</Text>
                  </Col>
                  <Col span={8}>
                    <Text strong>交工日期：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.handDate}</Text>
                  </Col>
                  <Col span={8}>
                    <Text strong>竣工日期：</Text>
                    <br />
                    <Text style={{ color: '#4b5563' }}>{record.endDate}</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={24}>
              <Card size="small" style={{ background: '#f0fdf4' }}>
                <Title level={5} style={{ margin: '0 0 12px 0', color: '#1f2937' }}>
                  财务信息
                </Title>
                <Row gutter={[16, 12]}>
                  <Col span={8}>
                    <Text strong>公里数：</Text>
                    <br />
                    <Text style={{ color: '#059669', fontWeight: 500 }}>{record.kilometer} km</Text>
                  </Col>
                  <Col span={8}>
                    <Text strong>合同金额：</Text>
                    <br />
                    <Text style={{ color: '#dc2626', fontWeight: 500 }}>{record.contractPrice}</Text>
                  </Col>
                  <Col span={8}>
                    <Text strong>结算金额：</Text>
                    <br />
                    <Text style={{ color: '#059669', fontWeight: 500 }}>{record.settlementPrice}</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={24}>
              <Card size="small" style={{ background: '#fefce8' }}>
                <Title level={5} style={{ margin: '0 0 12px 0', color: '#1f2937' }}>
                  主要业绩
                </Title>
                <div style={{
                  padding: '12px',
                  background: '#fff',
                  borderRadius: '6px',
                  border: '1px solid #e5e7eb',
                  lineHeight: '1.6'
                }}>
                  <Text style={{ color: '#4b5563' }}>{record.remark}</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ),
      okText: '关闭',
      okButtonProps: {
        style: { borderRadius: '6px' }
      }
    })
  }

  const handleTagClose = (tag: string) => {
    const newArray = keywordsArray.filter(item => item !== tag)
    setKeywordsArray(newArray)
    setKeyWordsStr(newArray.join(' '))
  }

  const onSubmit = async (values: SearchForm) => {
    setLoading(true)
    try {
      // Simulate API call
      const searchParams = {
        ...values,
        keyWords: keywordsArray.join(','),
        handDate: values.handDate ? values.handDate.join(',') : '',
        endDate: values.endDate ? values.endDate.join(',') : '',
      }
      
      console.log('Search params:', searchParams)
      
      // Mock data for demonstration
      const mockData: DataItem[] = Array.from({ length: 50 }, (_, i) => ({
        key: `${i}`,
        corpName: `企业${i + 1}`,
        projectName: `工程项目${i + 1}`,
        technologyGrade: ['高速公路', '一级公路', '二级公路'][i % 3],
        province: ['北京', '上海', '广东', '浙江'][i % 4],
        beginDate: '2020-01-01',
        handDate: '2021-01-01',
        endDate: '2021-06-01',
        kilometer: `${(i + 1) * 10}`,
        contractPrice: `${(i + 1) * 1000}万`,
        settlementPrice: `${(i + 1) * 950}万`,
        remark: `项目${i + 1}的主要业绩描述`
      }))
      
      setData(mockData)
      setTotal(mockData.length)
      setPageIndex(1)
      message.success('搜索完成')
    } catch (error) {
      message.error('搜索失败')
    } finally {
      setLoading(false)
    }
  }

  const onReset = () => {
    form.resetFields()
    setKeyWordsStr('')
    setKeywordsArray([])
    setData([])
    setTotal(0)
    setPageIndex(1)
  }

  const closeExpand = () => {
    setExpandedRowKeys([])
  }

  const downloadSelected = () => {
    try {
      if (data.length === 0) {
        message.warning('没有数据可以下载')
        return
      }
      exportTableToExcel(data, '企业搜索结果.xlsx')
      message.success('下载成功')
    } catch (error) {
      message.error('下载失败')
    }
  }

  const handlePageChange = (page: number, size: number) => {
    setPageIndex(page)
    setPageSize(size)
  }

  return (
    <div style={{ padding: '0 4px' }}>
      {/* Search Form Section */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SearchOutlined style={{ color: '#1890ff' }} />
            <Title level={4} style={{ margin: 0, color: '#1f2937' }}>
              企业信息检索
            </Title>
          </div>
        }
        style={{
          marginBottom: '16px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
        }}
        bodyStyle={{ padding: '24px' }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmit}
          size="middle"
        >
          {/* First Row - Basic Info */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={
                  <span style={{ fontWeight: 500 }}>
                    企业名称
                    <Tooltip title="请输入企业的完整全称">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c' }} />
                    </Tooltip>
                  </span>
                }
                name="corpName"
              >
                <Input
                  placeholder="请输入企业完整全称"
                  style={{ borderRadius: '6px' }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={<span style={{ fontWeight: 500 }}>交工日期范围</span>}
                name="handDate"
              >
                <RangePicker
                  style={{ width: '100%', borderRadius: '6px' }}
                  placeholder={['开始日期', '结束日期']}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={<span style={{ fontWeight: 500 }}>完成里程数 (km)</span>}
              >
                <Input.Group compact>
                  <Form.Item name="kilometerSmall" noStyle>
                    <InputNumber
                      placeholder="最小值"
                      style={{ width: '48%', borderRadius: '6px 0 0 6px' }}
                      min={0}
                    />
                  </Form.Item>
                  <span style={{
                    display: 'inline-block',
                    width: '4%',
                    textAlign: 'center',
                    lineHeight: '32px',
                    color: '#8c8c8c'
                  }}>
                    ~
                  </span>
                  <Form.Item name="kilometerBig" noStyle>
                    <InputNumber
                      placeholder="最大值"
                      style={{ width: '48%', borderRadius: '0 6px 6px 0' }}
                      min={0}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </Row>

          {/* Second Row - Status and Dates */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={<span style={{ fontWeight: 500 }}>修建状态</span>}
                name="constructStatus"
              >
                <Select
                  placeholder="请选择修建状态"
                  allowClear
                  style={{ borderRadius: '6px' }}
                >
                  {constructStatusOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={<span style={{ fontWeight: 500 }}>竣工日期范围</span>}
                name="endDate"
              >
                <RangePicker
                  style={{ width: '100%', borderRadius: '6px' }}
                  placeholder={['开始日期', '结束日期']}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label={<span style={{ fontWeight: 500 }}>合同金额 (万元)</span>}
              >
                <Input.Group compact>
                  <Form.Item name="priceSmall" noStyle>
                    <InputNumber
                      placeholder="最小值"
                      style={{ width: '48%', borderRadius: '6px 0 0 6px' }}
                      min={0}
                    />
                  </Form.Item>
                  <span style={{
                    display: 'inline-block',
                    width: '4%',
                    textAlign: 'center',
                    lineHeight: '32px',
                    color: '#8c8c8c'
                  }}>
                    ~
                  </span>
                  <Form.Item name="priceBig" noStyle>
                    <InputNumber
                      placeholder="最大值"
                      style={{ width: '48%', borderRadius: '0 6px 6px 0' }}
                      min={0}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </Row>

          {/* Third Row - Keywords */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item
                label={
                  <span style={{ fontWeight: 500 }}>
                    关键词搜索
                    <Tooltip title="使用空格分隔多个关键词，如：桥梁 路基 隧道">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#8c8c8c' }} />
                    </Tooltip>
                  </span>
                }
              >
                <Input
                  value={keyWordsStr}
                  onChange={(e) => setKeyWordsStr(e.target.value)}
                  placeholder="请输入关键词，使用空格分隔，如：桥梁 路基 隧道"
                  style={{ borderRadius: '6px' }}
                />
                {keywordsArray.length > 0 && (
                  <div style={{
                    marginTop: '12px',
                    padding: '12px',
                    background: '#f8f9fa',
                    borderRadius: '6px',
                    border: '1px solid #e9ecef'
                  }}>
                    <div style={{ marginBottom: '8px' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>已添加的关键词：</Text>
                    </div>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', alignItems: 'center' }}>
                      {keywordsArray.map(tag => (
                        <Tag
                          key={tag}
                          closable
                          color="processing"
                          onClose={() => handleTagClose(tag)}
                          style={{ margin: 0 }}
                        >
                          {tag}
                        </Tag>
                      ))}
                      <Form.Item name="keyType" style={{ margin: 0, marginLeft: '12px' }}>
                        <Radio.Group size="small">
                          <Radio.Button value="and">AND</Radio.Button>
                          <Radio.Button value="or">OR</Radio.Button>
                        </Radio.Group>
                      </Form.Item>
                    </div>
                  </div>
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* Action Buttons */}
          <Divider style={{ margin: '16px 0' }} />
          <Row justify="end">
            <Col>
              <Space size="middle">
                <Button
                  onClick={onReset}
                  icon={<ReloadOutlined />}
                  style={{ borderRadius: '6px' }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SearchOutlined />}
                  style={{
                    borderRadius: '6px',
                    background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                    border: 'none'
                  }}
                >
                  {loading ? '检索中...' : '开始检索'}
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Results Section */}
      {data.length > 0 && (
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <InfoCircleOutlined style={{ color: '#52c41a' }} />
                <Title level={4} style={{ margin: 0, color: '#1f2937' }}>
                  检索结果
                </Title>
                <Text type="secondary">
                  共找到 {total} 条记录
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>显示行号</Text>
                <Switch
                  checked={showIndex}
                  onChange={setShowIndex}
                  size="small"
                />
                <Divider type="vertical" />
                <Space size="small">
                  <Tooltip title="收起所有展开行">
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={closeExpand}
                    >
                      收起展开
                    </Button>
                  </Tooltip>
                  <Tooltip title="导出当前页数据到Excel">
                    <Button
                      type="primary"
                      size="small"
                      icon={<DownloadOutlined />}
                      onClick={downloadSelected}
                    >
                      导出数据
                    </Button>
                  </Tooltip>
                </Space>
              </div>
            </div>
          }
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
          bodyStyle={{ padding: '0' }}
        >
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            size="middle"
            style={{ borderRadius: '0 0 8px 8px' }}
            expandable={{
              expandedRowKeys,
              onExpandedRowsChange: (expandedKeys) => setExpandedRowKeys([...expandedKeys] as string[]),
              expandedRowRender: (record) => (
                <div style={{
                  padding: '16px 24px',
                  background: '#fafafa',
                  borderRadius: '6px',
                  margin: '8px 0'
                }}>
                  <Row gutter={[24, 12]}>
                    <Col xs={24} sm={12} lg={8}>
                      <div>
                        <Text strong style={{ color: '#1f2937' }}>合同金额：</Text>
                        <Text style={{ marginLeft: '8px', color: '#059669' }}>
                          {record.contractPrice}
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} sm={12} lg={8}>
                      <div>
                        <Text strong style={{ color: '#1f2937' }}>结算金额：</Text>
                        <Text style={{ marginLeft: '8px', color: '#dc2626' }}>
                          {record.settlementPrice}
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} sm={24} lg={8}>
                      <div>
                        <Text strong style={{ color: '#1f2937' }}>主要业绩：</Text>
                        <div style={{
                          marginTop: '4px',
                          padding: '8px 12px',
                          background: '#fff',
                          borderRadius: '4px',
                          border: '1px solid #e5e7eb'
                        }}>
                          <Text style={{ color: '#4b5563' }}>
                            {record.remark}
                          </Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              ),
            }}
          />

          {/* Pagination */}
          <div style={{
            padding: '16px 24px',
            borderTop: '1px solid #f0f0f0',
            background: '#fafafa',
            borderRadius: '0 0 8px 8px'
          }}>
            <Pagination
              current={pageIndex}
              pageSize={pageSize}
              total={total}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                <Text type="secondary">
                  显示第 {range[0]}-{range[1]} 条，共 {total} 条记录
                </Text>
              }
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
              style={{ textAlign: 'center' }}
            />
          </div>
        </Card>
      )}

      {/* Empty State */}
      {!loading && data.length === 0 && (
        <Card
          style={{
            textAlign: 'center',
            padding: '48px 24px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
        >
          <div style={{ color: '#8c8c8c' }}>
            <SearchOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Title level={4} style={{ color: '#8c8c8c' }}>
              暂无数据
            </Title>
            <Text type="secondary">
              请输入搜索条件并点击"开始检索"按钮进行搜索
            </Text>
          </div>
        </Card>
      )}
    </div>
  )
}
